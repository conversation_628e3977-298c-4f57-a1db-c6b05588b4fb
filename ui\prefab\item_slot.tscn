[gd_scene load_steps=4 format=3 uid="uid://bq8xvn2h3k8ys"]

[ext_resource type="Shader" uid="uid://c4kdwr5gj0e4x" path="res://assets/materials/shader/fluid_outline.gdshader" id="1_fqfc8"]
[ext_resource type="Script" uid="uid://ckywi7y6sn1tm" path="res://ui/prefab/item_slot.gd" id="2_script"]

[sub_resource type="ShaderMaterial" id="ShaderMaterial_1bday"]
shader = ExtResource("1_fqfc8")

[node name="ItemSlot" type="Button"]
material = SubResource("ShaderMaterial_1bday")
custom_minimum_size = Vector2(60, 90)
offset_right = 60.0
offset_bottom = 90.0
size_flags_horizontal = 6
size_flags_vertical = 4
theme_type_variation = &"IconSlot"
action_mode = 0
script = ExtResource("2_script")

[node name="VBoxContainer" type="VBoxContainer" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_constants/separation = 3
alignment = 1

[node name="TextureRect" type="TextureRect" parent="VBoxContainer"]
custom_minimum_size = Vector2(55, 55)
layout_mode = 2
size_flags_horizontal = 4
size_flags_vertical = 10
mouse_filter = 2
expand_mode = 1
stretch_mode = 5

[node name="LevelLabel" type="Label" parent="VBoxContainer"]
custom_minimum_size = Vector2(0, 22)
layout_mode = 2
size_flags_horizontal = 4
theme_override_font_sizes/font_size = 18
text = "1"
horizontal_alignment = 1

[connection signal="pressed" from="." to="." method="_on_slot_pressed"]
