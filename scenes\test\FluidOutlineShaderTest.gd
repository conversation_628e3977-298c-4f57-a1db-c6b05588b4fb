extends Control

## 流体轮廓着色器测试控制脚本
## 提供实时参数调节功能

@onready var test_sprite: Sprite2D = $TestSprite
@onready var border_width_slider: HSlider = $UI/BorderControls/BorderWidthSlider
@onready var time_scale_slider: HSlider = $UI/BorderControls/TimeScaleSlider
@onready var inner_border_toggle: CheckBox = $UI/BorderControls/InnerBorderToggle

var shader_material: ShaderMaterial

func _ready():
	# 获取着色器材质
	shader_material = test_sprite.material as ShaderMaterial
	
	if not shader_material:
		push_error("未找到着色器材质！")
		return
	
	# 连接UI控件信号
	_connect_ui_signals()
	
	# 创建默认测试纹理
	_create_test_texture()
	
	print("流体轮廓着色器测试场景已加载")
	print("请在Inspector中调整更多参数，或使用UI控件进行基础调节")

## 连接UI控件信号
func _connect_ui_signals():
	if border_width_slider:
		border_width_slider.value_changed.connect(_on_border_width_changed)
	
	if time_scale_slider:
		time_scale_slider.value_changed.connect(_on_time_scale_changed)
	
	if inner_border_toggle:
		inner_border_toggle.toggled.connect(_on_inner_border_toggled)

## 创建测试纹理
func _create_test_texture():
	# 创建一个简单的圆形测试纹理
	var image = Image.create(256, 256, false, Image.FORMAT_RGBA8)
	var center = Vector2(128, 128)
	var radius = 100.0
	
	for x in range(256):
		for y in range(256):
			var pos = Vector2(x, y)
			var distance = pos.distance_to(center)
			
			if distance <= radius:
				# 内部：白色，Alpha渐变
				var alpha = 1.0 - (distance / radius) * 0.3
				image.set_pixel(x, y, Color(1.0, 1.0, 1.0, alpha))
			else:
				# 外部：透明
				image.set_pixel(x, y, Color(0.0, 0.0, 0.0, 0.0))
	
	var texture = ImageTexture.new()
	texture.set_image(image)
	test_sprite.texture = texture

## 边框宽度改变回调
func _on_border_width_changed(value: float):
	if shader_material:
		shader_material.set_shader_parameter("border_width", value)

## 时间缩放改变回调
func _on_time_scale_changed(value: float):
	if shader_material:
		shader_material.set_shader_parameter("time_scale", value)

## 内边框切换回调
func _on_inner_border_toggled(enabled: bool):
	if shader_material:
		shader_material.set_shader_parameter("enable_inner_border", enabled)

## 处理键盘输入进行快速测试
func _input(event: InputEvent):
	if not event is InputEventKey or not event.pressed:
		return
	
	if not shader_material:
		return
	
	match event.keycode:
		KEY_1:
			# 预设1：火焰效果
			_apply_flame_preset()
		KEY_2:
			# 预设2：冰霜效果
			_apply_frost_preset()
		KEY_3:
			# 预设3：能量场效果
			_apply_energy_preset()
		KEY_R:
			# 重置为默认值
			_reset_to_defaults()

## 应用火焰预设
func _apply_flame_preset():
	shader_material.set_shader_parameter("border_color", Vector3(1.0, 0.3, 0.0))
	shader_material.set_shader_parameter("time_scale", 1.5)
	shader_material.set_shader_parameter("layer1_speed", 0.02)
	shader_material.set_shader_parameter("layer2_speed", 0.3)
	shader_material.set_shader_parameter("enable_shine", true)
	print("已应用火焰预设")

## 应用冰霜预设
func _apply_frost_preset():
	shader_material.set_shader_parameter("border_color", Vector3(0.3, 0.7, 1.0))
	shader_material.set_shader_parameter("time_scale", 0.5)
	shader_material.set_shader_parameter("layer1_speed", 0.005)
	shader_material.set_shader_parameter("layer2_speed", 0.1)
	shader_material.set_shader_parameter("enable_shine", false)
	print("已应用冰霜预设")

## 应用能量场预设
func _apply_energy_preset():
	shader_material.set_shader_parameter("border_color", Vector3(0.0, 1.0, 0.3))
	shader_material.set_shader_parameter("time_scale", 2.0)
	shader_material.set_shader_parameter("layer1_speed", 0.05)
	shader_material.set_shader_parameter("layer2_speed", 0.4)
	shader_material.set_shader_parameter("enable_shine", true)
	shader_material.set_shader_parameter("enable_inner_border", true)
	print("已应用能量场预设")

## 重置为默认值
func _reset_to_defaults():
	shader_material.set_shader_parameter("border_color", Vector3(1.0, 0.3, 0.0))
	shader_material.set_shader_parameter("time_scale", 1.0)
	shader_material.set_shader_parameter("border_width", 0.25)
	shader_material.set_shader_parameter("enable_inner_border", false)
	shader_material.set_shader_parameter("layer1_speed", 0.01)
	shader_material.set_shader_parameter("layer2_speed", 0.2)
	shader_material.set_shader_parameter("enable_shine", true)
	
	# 同步UI控件
	if border_width_slider:
		border_width_slider.value = 0.25
	if time_scale_slider:
		time_scale_slider.value = 1.0
	if inner_border_toggle:
		inner_border_toggle.button_pressed = false
	
	print("已重置为默认值")

func _exit_tree():
	print("流体轮廓着色器测试场景已退出")
