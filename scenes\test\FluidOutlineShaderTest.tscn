[gd_scene load_steps=5 format=3 uid="uid://bqxvhqxvhqxvh"]

[ext_resource type="Shader" uid="uid://c4kdwr5gj0e4x" path="res://assets/materials/shader/fluid_outline.gdshader" id="1_fluid_shader"]
[ext_resource type="Script" uid="uid://rchdfswk2w4g" path="res://scenes/test/FluidOutlineShaderTest.gd" id="2_test_script"]

[sub_resource type="ShaderMaterial" id="ShaderMaterial_1"]
shader = ExtResource("1_fluid_shader")

[sub_resource type="ImageTexture" id="ImageTexture_1"]

[node name="FluidOutlineShaderTest" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("2_test_script")

[node name="Background" type="ColorRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
color = Color(0.1, 0.1, 0.15, 1)

[node name="TestSprite" type="Sprite2D" parent="."]
material = SubResource("ShaderMaterial_1")
position = Vector2(576, 324)
scale = Vector2(2, 2)
texture = SubResource("ImageTexture_1")

[node name="UI" type="VBoxContainer" parent="."]
layout_mode = 1
anchors_preset = 2
anchor_top = 1.0
anchor_bottom = 1.0
offset_left = 20.0
offset_top = -300.0
offset_right = 300.0
offset_bottom = -20.0
grow_vertical = 0

[node name="Title" type="Label" parent="UI"]
layout_mode = 2
text = "流体轮廓着色器测试"
horizontal_alignment = 1

[node name="HSeparator" type="HSeparator" parent="UI"]
layout_mode = 2

[node name="BorderControls" type="VBoxContainer" parent="UI"]
layout_mode = 2

[node name="BorderWidthLabel" type="Label" parent="UI/BorderControls"]
layout_mode = 2
text = "边框宽度"

[node name="BorderWidthSlider" type="HSlider" parent="UI/BorderControls"]
layout_mode = 2
max_value = 1.0
step = 0.01
value = 0.25

[node name="TimeScaleLabel" type="Label" parent="UI/BorderControls"]
layout_mode = 2
text = "时间缩放"

[node name="TimeScaleSlider" type="HSlider" parent="UI/BorderControls"]
layout_mode = 2
max_value = 5.0
step = 0.1
value = 1.0

[node name="InnerBorderToggle" type="CheckBox" parent="UI/BorderControls"]
layout_mode = 2
text = "启用内边框"

[node name="Instructions" type="RichTextLabel" parent="UI"]
layout_mode = 2
size_flags_vertical = 3
bbcode_enabled = true
text = "[b]使用说明：[/b]
• 拖动滑块调整参数
• 勾选复选框启用内边框
• 需要为TestSprite设置纹理才能看到效果

[b]注意：[/b]
请在Inspector中为TestSprite节点设置一个带Alpha通道的纹理图片。"
