# 流体轮廓着色器使用说明

## 概述

这是一个基于Perlin噪声的流体轮廓着色器，可以为2D精灵创建动态的火焰、能量场或其他流体效果边框。着色器支持内外边框、光泽效果、以及丰富的参数调节。

## 文件位置

- **着色器文件**: `assets/materials/shader/fluid_outline.gdshader`
- **测试场景**: `scenes/test/FluidOutlineShaderTest.tscn`
- **测试脚本**: `scenes/test/FluidOutlineShaderTest.gd`

## 主要特性

### 1. 流体效果
- 基于Perlin噪声的动态边框
- 多层噪声混合
- 可调节的时间缩放和动画速度

### 2. 边框系统
- **外边框**: 主要的流体效果边框
- **内边框**: 可选的内部边框效果
- **渐变控制**: 精确的边框渐变控制

### 3. 视觉效果
- **光泽效果**: 模拟火焰或能量的闪烁
- **颜色自定义**: 完全可自定义的边框颜色
- **量化效果**: 创建层次分明的视觉效果

## 参数说明

### 基础参数

| 参数名 | 类型 | 范围 | 默认值 | 说明 |
|--------|------|------|--------|------|
| `main_texture` | Texture2D | - | - | 主纹理，需要Alpha通道 |
| `time_scale` | float | 0.0-5.0 | 1.0 | 时间缩放因子，控制动画速度 |

### 边框参数

| 参数名 | 类型 | 范围 | 默认值 | 说明 |
|--------|------|------|--------|------|
| `border_width` | float | 0.0-1.0 | 0.25 | 边框宽度 |
| `border_fade_outer` | float | 0.0-0.5 | 0.1 | 外边框渐变范围 |
| `border_fade_inner` | float | 0.0-0.5 | 0.01 | 内边框渐变范围 |
| `border_color` | Vector3 | 0.0-1.0 | (1.0, 0.3, 0.0) | 边框颜色（橙红色） |

### 内边框参数

| 参数名 | 类型 | 范围 | 默认值 | 说明 |
|--------|------|------|--------|------|
| `enable_inner_border` | bool | - | false | 启用内边框 |
| `inner_border_width` | float | 0.0-0.5 | 0.1 | 内边框宽度 |
| `inner_border_color` | Vector3 | 0.0-1.0 | (0.0, 0.5, 1.0) | 内边框颜色（蓝色） |
| `inner_border_fade` | float | 0.0-0.2 | 0.05 | 内边框渐变 |

### Perlin噪声参数

| 参数名 | 类型 | 范围 | 默认值 | 说明 |
|--------|------|------|--------|------|
| `first_octave` | int | 1-8 | 3 | 第一个八度 |
| `octaves` | int | 1-12 | 8 | 八度数量，影响细节层次 |
| `persistence` | float | 0.1-1.0 | 0.6 | 持续性，控制高频细节强度 |

### 噪声层参数

| 参数名 | 类型 | 范围 | 默认值 | 说明 |
|--------|------|------|--------|------|
| `layer1_speed` | float | 0.0-1.0 | 0.01 | 第一层噪声移动速度 |
| `layer1_intensity` | float | 0.0-5.0 | 2.0 | 第一层噪声强度 |
| `layer2_speed` | float | 0.0-1.0 | 0.2 | 第二层噪声移动速度 |
| `layer2_intensity` | float | 0.0-5.0 | 2.0 | 第二层噪声强度 |
| `noise1_weight` | float | 0.0-3.0 | 1.2 | 第一层噪声权重 |
| `noise2_weight` | float | 0.0-2.0 | 0.6 | 第二层噪声权重 |

### 光泽效果参数

| 参数名 | 类型 | 范围 | 默认值 | 说明 |
|--------|------|------|--------|------|
| `enable_shine` | bool | - | true | 启用光泽效果 |
| `shine_threshold` | float | 0.0-1.0 | 0.5 | 光泽触发阈值 |
| `shine_intensity` | float | 0.0-1.0 | 0.2 | 光泽强度 |
| `quantization_level` | float | 1.0-100.0 | 10.0 | 噪声量化级别 |
| `shine_quantization` | float | 1.0-100.0 | 50.0 | 光泽量化级别 |

### 纹理参数

| 参数名 | 类型 | 范围 | 默认值 | 说明 |
|--------|------|------|--------|------|
| `texture_scroll_speed` | float | 0.0-1.0 | 0.03 | 纹理滚动速度 |
| `texture_scale_x` | float | 0.1-10.0 | 8.0 | 纹理X轴缩放 |
| `alpha_threshold` | float | 0.0-1.0 | 0.5 | Alpha阈值 |
| `distance_samples` | int | 5-50 | 20 | 距离场采样数量 |

## 使用方法

### 1. 基础使用

```gdscript
# 创建ShaderMaterial
var material = ShaderMaterial.new()
material.shader = load("res://assets/materials/shader/fluid_outline.gdshader")

# 应用到Sprite2D
var sprite = $MySprite
sprite.material = material

# 设置主纹理（必须有Alpha通道）
material.set_shader_parameter("main_texture", my_texture)
```

### 2. 参数调节

```gdscript
# 调整边框颜色为蓝色
material.set_shader_parameter("border_color", Vector3(0.0, 0.5, 1.0))

# 加快动画速度
material.set_shader_parameter("time_scale", 2.0)

# 启用内边框
material.set_shader_parameter("enable_inner_border", true)
```

### 3. 预设效果

#### 火焰效果
```gdscript
material.set_shader_parameter("border_color", Vector3(1.0, 0.3, 0.0))
material.set_shader_parameter("time_scale", 1.5)
material.set_shader_parameter("layer1_speed", 0.02)
material.set_shader_parameter("layer2_speed", 0.3)
```

#### 冰霜效果
```gdscript
material.set_shader_parameter("border_color", Vector3(0.3, 0.7, 1.0))
material.set_shader_parameter("time_scale", 0.5)
material.set_shader_parameter("layer1_speed", 0.005)
material.set_shader_parameter("enable_shine", false)
```

#### 能量场效果
```gdscript
material.set_shader_parameter("border_color", Vector3(0.0, 1.0, 0.3))
material.set_shader_parameter("time_scale", 2.0)
material.set_shader_parameter("enable_inner_border", true)
```

## 测试场景使用

1. 打开 `scenes/test/FluidOutlineShaderTest.tscn`
2. 运行场景查看效果
3. 使用UI控件调节基础参数
4. 按键盘快捷键测试预设：
   - `1`: 火焰预设
   - `2`: 冰霜预设  
   - `3`: 能量场预设
   - `R`: 重置默认值

## 性能注意事项

- `distance_samples` 参数影响性能，建议保持在20以下
- `octaves` 参数越高计算越复杂，建议不超过10
- 在移动设备上可能需要降低采样数量

## 技术原理

1. **距离场计算**: 通过采样周围像素计算到边缘的距离
2. **Perlin噪声**: 生成自然的流体动画效果
3. **多层混合**: 组合不同频率的噪声创建复杂效果
4. **量化处理**: 创建层次分明的视觉效果
5. **光泽模拟**: 通过条件判断添加闪烁效果

## 常见问题

**Q: 为什么看不到效果？**
A: 确保纹理有Alpha通道，且Alpha值有明显的0-1变化。

**Q: 边框太粗或太细？**
A: 调节 `border_width` 和 `border_fade_outer` 参数。

**Q: 动画太快或太慢？**
A: 调节 `time_scale` 和各层的 `speed` 参数。

**Q: 内边框不显示？**
A: 确保 `enable_inner_border` 为true，并调节 `inner_border_width`。
