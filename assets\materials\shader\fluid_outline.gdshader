shader_type canvas_item;

// ===== 流体轮廓着色器参数 =====

// 基础纹理
uniform sampler2D main_texture : source_color, hint_default_white;

// 时间控制
uniform float time_scale : hint_range(0.0, 5.0) = 1.0; // 时间缩放因子

// 边框参数
uniform float border_width : hint_range(0.0, 1.0) = 0.25; // 边框宽度
uniform float border_fade_outer : hint_range(0.0, 0.5) = 0.1; // 外边框渐变
uniform float border_fade_inner : hint_range(0.0, 0.5) = 0.01; // 内边框渐变
uniform vec3 border_color : source_color = vec3(1.0, 0.3, 0.0); // 边框颜色

// 内边框支持
uniform bool enable_inner_border : hint_default(false) = false; // 启用内边框
uniform float inner_border_width : hint_range(0.0, 0.5) = 0.1; // 内边框宽度
uniform vec3 inner_border_color : source_color = vec3(0.0, 0.5, 1.0); // 内边框颜色
uniform float inner_border_fade : hint_range(0.0, 0.2) = 0.05; // 内边框渐变

// Perlin噪声参数
uniform int first_octave : hint_range(1, 8) = 3; // 第一个八度
uniform int octaves : hint_range(1, 12) = 8; // 八度数量
uniform float persistence : hint_range(0.1, 1.0) = 0.6; // 持续性

// 噪声层参数
uniform float layer1_speed : hint_range(0.0, 1.0) = 0.01; // 第一层噪声速度
uniform float layer1_intensity : hint_range(0.0, 5.0) = 2.0; // 第一层噪声强度
uniform float layer2_speed : hint_range(0.0, 1.0) = 0.2; // 第二层噪声速度
uniform float layer2_intensity : hint_range(0.0, 5.0) = 2.0; // 第二层噪声强度

// 噪声混合参数
uniform float noise1_weight : hint_range(0.0, 3.0) = 1.2; // 第一层噪声权重
uniform float noise2_weight : hint_range(0.0, 2.0) = 0.6; // 第二层噪声权重

// 量化参数
uniform float quantization_level : hint_range(1.0, 100.0) = 10.0; // 噪声量化级别
uniform float shine_quantization : hint_range(1.0, 100.0) = 50.0; // 光泽量化级别

// 光泽效果参数
uniform bool enable_shine : hint_default(true) = true; // 启用光泽效果
uniform float shine_threshold : hint_range(0.0, 1.0) = 0.5; // 光泽阈值
uniform float shine_intensity : hint_range(0.0, 1.0) = 0.2; // 光泽强度

// 纹理滚动参数
uniform float texture_scroll_speed : hint_range(0.0, 1.0) = 0.03; // 纹理滚动速度
uniform float texture_scale_x : hint_range(0.1, 10.0) = 8.0; // 纹理X轴缩放

// 距离场参数
uniform int distance_samples : hint_range(5, 50) = 20; // 距离场采样数量
uniform float alpha_threshold : hint_range(0.0, 1.0) = 0.5; // Alpha阈值

// ===== Perlin噪声实现 =====

// 基础噪声函数
float noise(int x, int y) {
    float fx = float(x);
    float fy = float(y);
    return 2.0 * fract(sin(dot(vec2(fx, fy), vec2(12.9898, 78.233))) * 43758.5453) - 1.0;
}

// 平滑噪声函数
float smooth_noise(int x, int y) {
    return noise(x, y) / 4.0 +
           (noise(x + 1, y) + noise(x - 1, y) + noise(x, y + 1) + noise(x, y - 1)) / 8.0 +
           (noise(x + 1, y + 1) + noise(x + 1, y - 1) + noise(x - 1, y + 1) + noise(x - 1, y - 1)) / 16.0;
}

// 余弦插值函数
float cos_interpolation(float x, float y, float n) {
    float r = n * PI;
    float f = (1.0 - cos(r)) * 0.5;
    return x * (1.0 - f) + y * f;
}

// 插值噪声函数
float interpolation_noise(float x, float y) {
    int ix = int(x);
    int iy = int(y);
    float fracx = x - float(ix);
    float fracy = y - float(iy);

    float v1 = smooth_noise(ix, iy);
    float v2 = smooth_noise(ix + 1, iy);
    float v3 = smooth_noise(ix, iy + 1);
    float v4 = smooth_noise(ix + 1, iy + 1);

    float i1 = cos_interpolation(v1, v2, fracx);
    float i2 = cos_interpolation(v3, v4, fracx);

    return cos_interpolation(i1, i2, fracy);
}

// Perlin噪声主函数
float perlin_noise_2d(float x, float y) {
    float sum = 0.0;
    float frequency = 0.0;
    float amplitude = 0.0;

    for (int i = first_octave; i < octaves + first_octave; i++) {
        frequency = pow(2.0, float(i));
        amplitude = pow(persistence, float(i));
        sum += interpolation_noise(x * frequency, y * frequency) * amplitude;
    }

    return sum;
}

// ===== 轮廓颜色计算函数 =====

// 计算轮廓颜色，包含流体效果
vec3 calculate_outline_color(vec2 frag_coord, vec2 screen_size, float outer_factor) {
    vec2 uv = frag_coord / screen_size;

    // 应用时间缩放
    float t = TIME * time_scale + 100.0;

    float x = uv.x;

    // 第一层噪声
    float x1 = x + t * layer1_speed;
    float noise1 = 0.5 + layer1_intensity * perlin_noise_2d(x1, uv.y);

    // 第二层噪声
    float x2 = x + t * layer2_speed;
    float noise2 = 0.5 + layer2_intensity * perlin_noise_2d(x2, uv.y);

    // 混合噪声
    float noise = noise1_weight * noise1 + noise2_weight * noise2;

    // 量化噪声以产生层次效果
    float quantized_noise = floor(noise * quantization_level) / quantization_level;
    float shine_quantized = floor(noise * shine_quantization) / shine_quantization;
    float shine_check = floor(noise2 * shine_quantization);

    float final_value = quantized_noise;

    // 添加光泽效果
    if (enable_shine &&
        (shine_quantized == 0.7 || shine_quantized == 0.9 || shine_quantized == 0.5 ||
         shine_quantized == 1.1 || shine_quantized == 0.5) &&
        noise2 > shine_threshold) {
        final_value = shine_intensity + shine_intensity * noise2;
    }

    // 应用外边框因子
    final_value -= outer_factor * 0.1;

    // 返回火焰色彩（橙红色调）
    return vec3(2.0 - 2.0 * final_value, 1.0 - 1.0 * final_value, 0.0 - 2.0 * final_value);
}

// ===== 主着色器函数 =====

void fragment() {
    vec2 screen_size = 1.0 / SCREEN_PIXEL_SIZE;
    float r = min(screen_size.x, screen_size.y);

    // 计算UV坐标
    vec2 uv = FRAGCOORD.xy / r;
    uv.x /= texture_scale_x;
    uv.y = 1.0 - uv.y;

    // 采样主纹理（带滚动效果）
    vec2 scroll_offset = vec2(TIME * texture_scroll_speed, 0.0);
    vec4 tex_sample = texture(main_texture, uv - scroll_offset);
    vec3 base_color = tex_sample.rgb;
    float alpha = tex_sample.a;

    // 判断当前像素是否在形状内部
    bool is_inside = step(alpha_threshold, alpha) == 1.0;

    // 距离场计算参数
    int half_samples = distance_samples / 2;
    float min_distance = float(distance_samples);

    // 计算到边缘的距离
    for (int x = -half_samples; x < half_samples; x++) {
        for (int y = -half_samples; y < half_samples; y++) {
            vec2 offset = vec2(float(x), float(y));
            vec2 sample_pos = (FRAGCOORD.xy + offset) / r;
            sample_pos.x /= texture_scale_x;
            sample_pos.y = 1.0 - sample_pos.y;

            float sample_alpha = texture(main_texture, sample_pos - scroll_offset).a;
            bool sample_inside = step(alpha_threshold, sample_alpha) == 1.0;

            // 如果当前像素和采样像素的内外状态不同，更新最小距离
            if ((!is_inside && sample_inside) || (is_inside && !sample_inside)) {
                min_distance = min(min_distance, length(offset));
            }
        }
    }

    // 归一化距离并转换为距离场
    min_distance = clamp(min_distance, 0.0, float(distance_samples)) / float(distance_samples);

    // 内部为负值，外部为正值
    if (is_inside) {
        min_distance = -min_distance;
    }

    // 转换为0-1范围并反转
    min_distance = min_distance * 0.5 + 0.5;
    min_distance = 1.0 - min_distance;

    // 计算边框效果
    float outer_factor = smoothstep(0.5 - (border_width + border_fade_outer), 0.5, min_distance);

    // 计算轮廓颜色
    vec3 outline_color = calculate_outline_color(FRAGCOORD.xy, screen_size, outer_factor);
    vec4 border_result = mix(vec4(0.0, 0.0, 0.0, 0.0), vec4(outline_color, 1.0), outer_factor);

    // 内边框效果
    float inner_factor = smoothstep(0.5, 0.5 + border_fade_inner, min_distance);
    vec4 final_color = mix(border_result, vec4(base_color, 1.0), inner_factor);

    // 内边框支持
    if (enable_inner_border && is_inside) {
        float inner_border_factor = smoothstep(0.5 + border_fade_inner,
                                             0.5 + border_fade_inner + inner_border_width,
                                             min_distance);
        float inner_border_blend = smoothstep(0.5 + border_fade_inner + inner_border_width - inner_border_fade,
                                            0.5 + border_fade_inner + inner_border_width,
                                            min_distance);

        vec4 inner_border_result = mix(vec4(inner_border_color, 1.0), final_color, inner_border_blend);
        final_color = mix(final_color, inner_border_result, 1.0 - inner_border_factor);
    }

    COLOR = final_color;
}

void vertex() {
	// Called for every vertex the material is visible on.
}

void fragment() {
	// Called for every pixel the material is visible on.
}

//void light() {
//	// Called for every pixel for every light affecting the CanvasItem.
//	// Uncomment to replace the default light processing function with this one.
//}
